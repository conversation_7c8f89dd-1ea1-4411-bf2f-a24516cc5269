import Cocoa
import lingxia
import os.log

// Device size configurations for different Apple devices
enum DeviceSize: String, CaseIterable {
    case iphone = "iphone"
    case ipad = "ipad"
    case ipadLandscape = "ipad-landscape"
    case ipadAir = "ipadair"
    case ipadAirLandscape = "ipadair-landscape"
    case ipadPro11 = "ipadpro11"
    case ipadPro11Landscape = "ipadpro11-landscape"
    case ipadPro129 = "ipadpro129"
    case ipadPro129Landscape = "ipadpro129-landscape"
    case custom = "custom"
    
    var size: (width: CGFloat, height: CGFloat) {
        switch self {
        case .iphone:
            return (414, 896) // iPhone 11 Pro Max size
        case .ipad:
            return (810, 1080) // iPad 10.2" portrait
        case .ipadLandscape:
            return (1080, 810) // iPad 10.2" landscape
        case .ipadAir:
            return (820, 1180) // iPad Air 10.9" portrait
        case .ipadAirLandscape:
            return (1180, 820) // iPad Air 10.9" landscape
        case .ipadPro11:
            return (834, 1194) // iPad Pro 11" portrait
        case .ipadPro11Landscape:
            return (1194, 834) // iPad Pro 11" landscape
        case .ipadPro129:
            return (1024, 1366) // iPad Pro 12.9" portrait
        case .ipadPro129Landscape:
            return (1366, 1024) // iPad Pro 12.9" landscape
        case .custom:
            return (414, 896) // Default to iPhone if custom
        }
    }
    
    var description: String {
        switch self {
        case .iphone:
            return "iPhone (414x896)"
        case .ipad:
            return "iPad 10.2\" Portrait (810x1080)"
        case .ipadLandscape:
            return "iPad 10.2\" Landscape (1080x810)"
        case .ipadAir:
            return "iPad Air 10.9\" Portrait (820x1180)"
        case .ipadAirLandscape:
            return "iPad Air 10.9\" Landscape (1180x820)"
        case .ipadPro11:
            return "iPad Pro 11\" Portrait (834x1194)"
        case .ipadPro11Landscape:
            return "iPad Pro 11\" Landscape (1194x834)"
        case .ipadPro129:
            return "iPad Pro 12.9\" Portrait (1024x1366)"
        case .ipadPro129Landscape:
            return "iPad Pro 12.9\" Landscape (1366x1024)"
        case .custom:
            return "Custom size"
        }
    }
}

// Global variable to store selected device size
nonisolated(unsafe) var selectedDeviceSize: DeviceSize = .iphone

@MainActor
class AppDelegate: NSObject, NSApplicationDelegate {
    private static var hasInitialized = false
    private let log = OSLog(subsystem: "LingXia.LxApp.macOS", category: "App")

    func applicationDidFinishLaunching(_ aNotification: Notification) {
        // Ensure app activation
        NSApp.setActivationPolicy(.regular)
        NSApp.activate(ignoringOtherApps: true)

        if !Self.hasInitialized {
            Self.hasInitialized = true

            // Open home LxApp using dynamic configuration
            print("Opening LxApp window...")
            macOSLxApp.openHomeLxApp()
        }
    }

    func applicationWillTerminate(_ aNotification: Notification) {
        // Clean up resources
    }

    func applicationSupportsSecureRestorableState(_ app: NSApplication) -> Bool {
        return true
    }

    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        return true
    }
}

// Parse command line arguments for window size selection
func parseCommandLineArguments() {
    let arguments = CommandLine.arguments
    
    // Look for --size argument
    if let sizeIndex = arguments.firstIndex(of: "--size"),
       sizeIndex + 1 < arguments.count {
        let sizeValue = arguments[sizeIndex + 1].lowercased()
        if let deviceSize = DeviceSize(rawValue: sizeValue) {
            selectedDeviceSize = deviceSize
        } else {
            print("Invalid size option: \(sizeValue)")
            printUsage()
        }
    }
    
    // Look for --help argument
    if arguments.contains("--help") || arguments.contains("-h") {
        printUsage()
        exit(0)
    }
}

func printUsage() {
    print("LingXia Demo App - Window Size Options")
    print("Usage: LingXiaDemo [--size <device>] [--help]")
    print("")
    print("Options:")
    print("  --size <device>  Set window size (default: iphone)")
    print("                   Available devices:")
    for device in DeviceSize.allCases {
        print("                     \(device.rawValue) - \(device.description)")
    }
    print("  --help, -h       Show this help message")
    print("")
    print("Examples:")
    print("  LingXiaDemo --size iphone")
    print("  LingXiaDemo --size ipadpro11")
}

// Main entry point
parseCommandLineArguments()

let app = NSApplication.shared
let delegate = AppDelegate()
app.delegate = delegate
app.run()
