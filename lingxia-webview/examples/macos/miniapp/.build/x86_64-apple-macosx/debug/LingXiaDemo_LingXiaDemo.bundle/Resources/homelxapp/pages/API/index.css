* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    width: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background-color: #f7f7f7;
    color: #333;
}

.container {
    padding: 0;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

.header {
    background-color: #ffffff;
    padding: 24px 16px;
    text-align: center;
    border-bottom: 1px solid #e5e5e5;
}

.icon {
    font-size: 48px;
    margin-bottom: 16px;
}

h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #333;
}

.header p {
    font-size: 14px;
    color: #666;
    line-height: 1.5;
    max-width: 320px;
    margin: 0 auto;
}

.category {
    background-color: #ffffff;
    border-bottom: 1px solid #e5e5e5;
}

.category-header {
    display: flex;
    align-items: center;
    padding: 16px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.category-header:hover {
    background-color: #f8f8f8;
}

.category-icon {
    font-size: 20px;
    margin-right: 12px;
    width: 24px;
    text-align: center;
}

.category-title {
    flex: 1;
    font-size: 16px;
    color: #333;
}

.category-toggle {
    font-size: 16px;
    color: #c7c7cc;
    transition: transform 0.2s;
}

.category-content {
    border-top: 1px solid #e5e5e5;
}

.api-item {
    display: block;
    padding: 16px 16px 16px 52px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.api-item:hover {
    background-color: #f8f8f8;
}

.api-name {
    font-size: 16px;
    color: #333;
    margin-bottom: 8px;
}

.api-status {
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    display: inline-block;
    min-width: 80px;
    text-align: center;
}

.api-status.loading {
    background-color: #fff3cd;
    color: #856404;
}

.api-status.success {
    background-color: #d4edda;
    color: #155724;
}

.api-status.error {
    background-color: #f8d7da;
    color: #721c24;
}

.device-info {
    margin-top: 12px;
    padding: 12px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    font-size: 13px;
    display: none;
}

.device-info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 6px 0;
    padding: 4px 0;
}

.device-info-item:not(:last-child) {
    border-bottom: 1px solid #e9ecef;
    padding-bottom: 8px;
    margin-bottom: 8px;
}

.device-info-label {
    font-weight: 500;
    color: #495057;
    flex-shrink: 0;
    margin-right: 12px;
}

.device-info-value {
    color: #6c757d;
    font-weight: 400;
    text-align: right;
    word-break: keep-all;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 60%;
}

 