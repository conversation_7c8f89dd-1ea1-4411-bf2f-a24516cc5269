import { generateId, formatDate, validateForm } from '../../common/utils.js';

Page({
  data: {
    todos: [
      {
        id: 1,
        text: '学习 LingXia 框架',
        completed: false,
        createdAt: new Date('2024-01-01')
      },
      {
        id: 2,
        text: '实现统一 logic.js',
        completed: true,
        createdAt: new Date('2024-01-02')
      },
      {
        id: 3,
        text: '优化构建流程',
        completed: false,
        createdAt: new Date('2024-01-03')
      }
    ],
    newTodo: ''
  },

  onLoad() {
    console.log('Todo page loaded');
    // Format dates for display
    const formattedTodos = this.data.todos.map(todo => ({
      ...todo,
      createdAt: new Date(todo.createdAt),
      formattedDate: formatDate(new Date(todo.createdAt))
    }));
    this.setData({ todos: formattedTodos });
  },

  onShow() {
    console.log('Todo page shown');
  },

  // Page event handlers for testing
  onPageScroll(e) {
    console.log('📜 [Todo] Page scroll event:', {
      scrollTop: e.scrollTop,
      scrollLeft: e.scrollLeft
    });
    
    // Update event counter and status
    if (typeof window !== 'undefined' && window.eventCounts) {
      window.eventCounts.scroll++;
      window.updateEventStatus && window.updateEventStatus();
    }
  },

  onPullDownRefresh() {
    console.log('🔄 [Todo] Pull down refresh triggered');
    
    // Update event counter and status
    if (typeof window !== 'undefined' && window.eventCounts) {
      window.eventCounts.pull++;
      window.updateEventStatus && window.updateEventStatus();
    }
    
    // Simulate refresh completion
    setTimeout(() => {
      console.log('✅ [Todo] Refresh completed');
    }, 1000);
  },

  onReachBottom() {
    console.log('⬇️ [Todo] Reached bottom of page');
    
    // Update event counter and status
    if (typeof window !== 'undefined' && window.eventCounts) {
      window.eventCounts.bottom++;
      window.updateEventStatus && window.updateEventStatus();
    }
  },

  onResize(e) {
    console.log('📐 [Todo] Page resize event:', {
      windowWidth: e.windowWidth,
      windowHeight: e.windowHeight
    });
    
    // Update event counter and status
    if (typeof window !== 'undefined' && window.eventCounts) {
      window.eventCounts.resize++;
      window.updateEventStatus && window.updateEventStatus();
    }
  },

  addTodo() {
    const todoText = this.data.newTodo.trim();
    
    // Validate input using common utility
    const validation = validateForm(
      { todo: todoText },
      { 
        todo: { 
          required: true, 
          minLength: 2,
          message: 'Todo must be at least 2 characters'
        }
      }
    );
    
    if (!validation.isValid) {
      console.warn('Validation failed:', validation.errors);
      return;
    }

    // Generate unique ID using common utility
    const newTodo = {
      id: generateId(),
      text: todoText,
      completed: false,
      createdAt: new Date(),
      formattedDate: formatDate(new Date())
    };

    const newTodos = [...this.data.todos, newTodo];
    this.setData({
      todos: newTodos,
      newTodo: ''
    });
  },

  toggleTodo(event) {
    const todoId = event.currentTarget.dataset.id;
    const updatedTodos = this.data.todos.map(todo =>
      todo.id === todoId ? { ...todo, completed: !todo.completed } : todo
    );
    this.setData({ todos: updatedTodos });
  },

  deleteTodo(event) {
    const todoId = event.currentTarget.dataset.id;
    const filteredTodos = this.data.todos.filter(todo => todo.id !== todoId);
    this.setData({ todos: filteredTodos });
  },

  onInputChange(event) {
    this.setData({ newTodo: event.detail.value });
  }
});