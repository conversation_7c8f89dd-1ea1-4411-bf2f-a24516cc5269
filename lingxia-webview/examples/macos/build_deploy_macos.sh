#!/bin/bash

# Exit on error
set -e

# Get the absolute path of the script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$SCRIPT_DIR/../.."
LINGXIA_ROOT="$PROJECT_ROOT/../" # LingXia project root directory
WORKSPACE_ROOT="$LINGXIA_ROOT" # Workspace root is the same as LingXia root

# Define the resources directory for macOS
RESOURCES_DIR="$SCRIPT_DIR/miniapp/Sources/Resources"

echo "Building Rust library for macOS with Swift bridge headers..."
cd "$WORKSPACE_ROOT"

# Build for macOS (static library for linking)
if [ "$(uname -m)" = "arm64" ]; then
    echo "Building for Apple Silicon (arm64)..."
    cargo rustc --crate-type=staticlib --release --target aarch64-apple-darwin -p lingxia --manifest-path lingxia-webview/Cargo.toml
else
    echo "Building for Intel (x86_64)..."
    cargo rustc --crate-type=staticlib --release --target x86_64-apple-darwin -p lingxia --manifest-path lingxia-webview/Cargo.toml
fi

mkdir -p "$RESOURCES_DIR"

# Clean resources directory before copying new files
echo "Cleaning resources directory..."
rm -rf "$RESOURCES_DIR"/*

echo "Copying lingxia-view files to resources..."
cp "$LINGXIA_ROOT/lingxia-view/404.html" "$RESOURCES_DIR/"
cp "$LINGXIA_ROOT/lingxia-view/webview-bridge.js" "$RESOURCES_DIR/"

echo "Copying host app configuration..."
cp "$LINGXIA_ROOT/examples/demo/app.json" "$RESOURCES_DIR/"

echo "Building and copying demo LxApp..."
cd "$LINGXIA_ROOT/examples/demo/homelxapp"
if [ -f "package.json" ] ; then
    echo "Building homelxapp with Vite..."
    npm install --silent
    npm run build

    # Copy built LxApp to resources with proper directory structure
    if [ -d "dist" ]; then
        echo "Copying built LxApp to resources..."
        mkdir -p "$RESOURCES_DIR/homelxapp"
        cp -R dist/* "$RESOURCES_DIR/homelxapp/"
    else
        echo "Warning: dist directory not found, copying source files..."
        cp -R . "$RESOURCES_DIR/homelxapp/"
    fi
else
    mkdir -p "$RESOURCES_DIR/homelxapp"
    cp -R "$LINGXIA_ROOT/examples/demo/homelxapp/"* "$RESOURCES_DIR/homelxapp/"
fi

echo "Building and running macOS app..."
cd "$SCRIPT_DIR/miniapp"

# Use the existing build script
chmod +x build.sh
./build.sh "$@"
