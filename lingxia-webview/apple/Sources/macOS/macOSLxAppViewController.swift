#if os(macOS)
import Foundation
import WebKit
import os.log
import Cocoa
import CLingXiaFFI

private let lxAppViewControllerLog = OSLog(subsystem: "LingXia", category: "LxAppView")

@MainActor
public class macOSLxAppViewController: NSViewController, WKNavigationDelegate {
    nonisolated(unsafe) private static let log = lxAppViewControllerLog

    // MARK: - Constants (from working macOS implementation)
    private static let TAB_BAR_HEIGHT: CGFloat = 40
    internal static let DEFAULT_NAV_BAR_HEIGHT: CGFloat = 32
    internal static let DEFAULT_TAB_BAR_SIZE: CGFloat = 40
    internal static let STATUS_BAR_HEIGHT: CGFloat = 28
    internal static let CAPSULE_BUTTON_HEIGHT: CGFloat = 32
    internal static let CAPSULE_BUTTON_WIDTH: CGFloat = 87
    internal static let CAPSULE_TOP_MARGIN: CGFloat = 4
    internal static let CAPSULE_RIGHT_MARGIN: CGFloat = 7

    // MARK: - Properties (from working macOS implementation)
    internal var appId: String
    private var initialPath: String
    private var webViewContainer: NSView!
    private var tabBarView: NSView?
    private var isDisplayingHomeLxApp: Bool = false
    private var currentWebView: WKWebView?
    private var rootContainer: NSView!
    private var statusBarBackground: NSView!
    private var navigationBar: NavigationBarProtocol?

    nonisolated(unsafe) private var closeAppObserver: NSObjectProtocol?
    nonisolated(unsafe) private var switchPageObserver: NSObjectProtocol?

    public init(appId: String, path: String) {
        self.appId = appId
        self.initialPath = path
        self.isDisplayingHomeLxApp = SharedLxApp.isHomeLxApp(appId)
        super.init(nibName: nil, bundle: nil)
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        os_log("Deinitializing LxAppViewController for appId: %@", log: Self.log, type: .info, appId)
        if let observer = closeAppObserver {
            NotificationCenter.default.removeObserver(observer)
        }
        if let observer = switchPageObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }

    // MARK: - Lifecycle (from working macOS implementation)
    public override func loadView() {
        view = NSView()
        view.wantsLayer = true
        view.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor
    }

    public override func viewDidLoad() {
        super.viewDidLoad()
        print("🚀 viewDidLoad started for appId: \(appId)")
        os_log("🚀 viewDidLoad started for appId: %@", log: Self.log, type: .info, appId)

        // 完全照搬 macosx 实验实现的布局流程
        print("🔧 Calling setupRootContainer")
        setupRootContainer()
        print("🔧 Calling setupStatusBar")
        setupStatusBar()
        print("🔧 Calling setupNavigationBar")
        setupNavigationBar()
        print("🔧 Calling setupTabBar")
        setupTabBar()
        print("🔧 Calling setupWebViewIfReady")
        setupWebViewIfReady()
        print("🔧 Calling setupNotificationObservers")
        setupNotificationObservers()

        print("🚀 viewDidLoad completed")
        os_log("🚀 viewDidLoad completed", log: Self.log, type: .info)
    }

    public override func viewDidAppear() {
        super.viewDidAppear()
        setupTitleBarIntegration()
    }

    // MARK: - UI Setup (from working macOS implementation)
    private func setupRootContainer() {
        print("🔧 setupRootContainer: Creating rootContainer")
        os_log("🔧 setupRootContainer: Creating rootContainer", log: Self.log, type: .info)

        rootContainer = NSView()
        rootContainer.wantsLayer = true
        rootContainer.translatesAutoresizingMaskIntoConstraints = false
        view.addSubview(rootContainer)

        print("🔧 setupRootContainer: rootContainer created and added to view")
        os_log("🔧 setupRootContainer: rootContainer created and added to view", log: Self.log, type: .info)

        // Leave space for the title bar (32pt height)
        NSLayoutConstraint.activate([
            rootContainer.topAnchor.constraint(equalTo: view.topAnchor, constant: 32),
            rootContainer.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            rootContainer.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            rootContainer.bottomAnchor.constraint(equalTo: view.bottomAnchor)
        ])
    }

    private func setupStatusBar() {
        statusBarBackground = NSView()
        statusBarBackground.wantsLayer = true
        statusBarBackground.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor
        statusBarBackground.translatesAutoresizingMaskIntoConstraints = false
        rootContainer.addSubview(statusBarBackground)

        NSLayoutConstraint.activate([
            statusBarBackground.topAnchor.constraint(equalTo: rootContainer.topAnchor),
            statusBarBackground.leadingAnchor.constraint(equalTo: rootContainer.leadingAnchor),
            statusBarBackground.trailingAnchor.constraint(equalTo: rootContainer.trailingAnchor),
            statusBarBackground.heightAnchor.constraint(equalToConstant: Self.STATUS_BAR_HEIGHT)
        ])
    }

    // 照搬 macosx 实验实现的 setupNavigationBar (空实现，使用自定义标题栏)
    private func setupNavigationBar() {
        // Navigation bar setup if needed - 使用自定义标题栏代替
        os_log("🔧 setupNavigationBar completed (using custom title bar)", log: Self.log, type: .info)
    }



    private func setupWebViewContainer() {
        // Create WebView container if it doesn't exist
        if webViewContainer == nil {
            webViewContainer = NSView()
            webViewContainer.wantsLayer = true
            webViewContainer.translatesAutoresizingMaskIntoConstraints = false
            rootContainer.addSubview(webViewContainer)

            // Setup constraints - account for NavigationBar at top and TabBar at bottom
            let navigationBarHeight: CGFloat = 44  // NavigationBar height
            let tabBarHeight: CGFloat = 48         // TabBar height

            NSLayoutConstraint.activate([
                webViewContainer.topAnchor.constraint(equalTo: rootContainer.topAnchor, constant: navigationBarHeight),
                webViewContainer.leadingAnchor.constraint(equalTo: rootContainer.leadingAnchor),
                webViewContainer.trailingAnchor.constraint(equalTo: rootContainer.trailingAnchor),
                webViewContainer.bottomAnchor.constraint(equalTo: rootContainer.bottomAnchor, constant: -tabBarHeight)
            ])

            os_log("WebView container created with NavigationBar height: %f, TabBar height: %f", log: Self.log, type: .info, navigationBarHeight, tabBarHeight)
        }
    }

    // MARK: - TabBar Setup

    // 照搬 macosx 实验实现的 setupTabBar
    private func setupTabBar() {
        print("🔧 setupTabBar started for appId: \(appId)")
        os_log("🔧 setupTabBar started for appId: %@", log: Self.log, type: .info, appId)

        print("🔧 About to call getTabBarConfig")
        guard let tabBarConfigRust = getTabBarConfig(appId) else {
            print("❌ No TabBar config found for appId: \(appId)")
            os_log("❌ No TabBar config found for appId: %@", log: Self.log, type: .info, appId)
            return
        }
        print("✅ Got TabBar config from Rust")

        print("🔧 About to convert Rust config to JSON string")
        let tabBarConfigJson = tabBarConfigRust.toString()
        print("🔧 TabBar config JSON: \(tabBarConfigJson)")

        print("🔧 About to parse TabBar config from JSON")
        guard let tabBarConfig = TabBarConfig.fromJson(tabBarConfigJson) else {
            print("❌ Failed to parse TabBar config for appId: \(appId)")
            print("❌ JSON was: \(tabBarConfigJson)")
            os_log("❌ Failed to parse TabBar config for appId: %@", log: Self.log, type: .error, appId)
            return
        }
        print("✅ Successfully parsed TabBar config")

        // 创建 TabBar 视图并添加按钮
        let tabBarView = NSView()
        tabBarView.wantsLayer = true
        tabBarView.layer?.backgroundColor = (tabBarConfig.backgroundColor ?? TabBarConfig.DEFAULT_BACKGROUND_COLOR).cgColor
        tabBarView.translatesAutoresizingMaskIntoConstraints = false

        // 创建水平 StackView 来放置 TabBar 按钮
        let stackView = NSStackView()
        stackView.orientation = .horizontal
        stackView.distribution = .fillEqually
        stackView.alignment = .centerY
        stackView.spacing = 0
        stackView.translatesAutoresizingMaskIntoConstraints = false
        tabBarView.addSubview(stackView)

        NSLayoutConstraint.activate([
            stackView.leadingAnchor.constraint(equalTo: tabBarView.leadingAnchor),
            stackView.trailingAnchor.constraint(equalTo: tabBarView.trailingAnchor),
            stackView.topAnchor.constraint(equalTo: tabBarView.topAnchor),
            stackView.bottomAnchor.constraint(equalTo: tabBarView.bottomAnchor)
        ])

        // 为每个 TabBar 项创建按钮
        for (index, item) in tabBarConfig.items.enumerated() {
            let button = NSButton(title: item.text, target: self, action: #selector(tabButtonClicked(_:)))
            button.tag = index
            button.bezelStyle = .regularSquare
            button.isBordered = false
            button.wantsLayer = true
            button.layer?.backgroundColor = NSColor.clear.cgColor
            button.contentTintColor = NSColor.secondaryLabelColor

            // 设置字体
            button.font = NSFont.systemFont(ofSize: 10, weight: .medium)

            // 设置图标
            if let iconPath = item.iconPath {
                setButtonIcon(button: button, iconPath: iconPath)
            }

            // 设置图标在上方，文字在下方
            button.imagePosition = .imageAbove
            button.imageScaling = .scaleProportionallyDown

            stackView.addArrangedSubview(button)
        }

        self.tabBarView = tabBarView
        os_log("✅ TabBar setup completed with %d items", log: Self.log, type: .info, tabBarConfig.items.count)
    }

    // 照搬 macosx 实验实现的 setupWebViewIfReady (完整布局逻辑)
    private func setupWebViewIfReady() {
        print("🔧 setupWebViewIfReady started for appId: \(appId), path: \(initialPath)")
        os_log("🔧 setupWebViewIfReady started for appId: %@, path: %@", log: Self.log, type: .info, appId, initialPath)

        print("🔧 Step 1: About to check rootContainer")
        os_log("🔧 Step 1: About to check rootContainer", log: Self.log, type: .info)

        // 详细检查 rootContainer 的状态
        if self.rootContainer == nil {
            print("❌ rootContainer is nil!")
            os_log("❌ rootContainer is nil!", log: Self.log, type: .error)
        } else {
            print("✅ rootContainer exists!")
            os_log("✅ rootContainer exists!", log: Self.log, type: .info)
        }

        // 再次检查 rootContainer 是否存在（在 guard 之前）
        print("🔧 About to perform guard check, rootContainer is: \(self.rootContainer == nil ? "nil" : "not nil")")
        os_log("🔧 About to perform guard check, rootContainer is: %@", log: Self.log, type: .info, self.rootContainer == nil ? "nil" : "not nil")

        // 检查 rootContainer 是否存在
        guard let rootContainer = self.rootContainer else {
            print("❌ Guard check failed: rootContainer is nil, cannot setup WebView")
            os_log("❌ Guard check failed: rootContainer is nil, cannot setup WebView", log: Self.log, type: .error)
            return
        }
        print("✅ Guard check passed: rootContainer exists, proceeding with WebView setup")
        os_log("✅ Guard check passed: rootContainer exists, proceeding with WebView setup", log: Self.log, type: .info)

        print("🔧 Step 2: About to create webViewContainer")
        os_log("🔧 Step 2: About to create webViewContainer", log: Self.log, type: .info)

        print("🔧 Step 2.1: About to create NSView()")
        webViewContainer = NSView()
        print("🔧 Step 2.2: NSView() created successfully")

        print("🔧 Step 2.3: Setting wantsLayer = true")
        webViewContainer.wantsLayer = true
        print("🔧 Step 2.4: wantsLayer set successfully")

        print("🔧 Step 2.5: Setting translatesAutoresizingMaskIntoConstraints = false")
        webViewContainer.translatesAutoresizingMaskIntoConstraints = false
        print("🔧 Step 2.6: translatesAutoresizingMaskIntoConstraints set successfully")

        print("🔧 Step 2.7: About to call rootContainer.addSubview(webViewContainer)")
        rootContainer.addSubview(webViewContainer)
        print("🔧 Step 2.8: rootContainer.addSubview(webViewContainer) completed successfully")

        print("🔧 Step 3: About to log webViewContainer success")
        print("🔧 Step 3.0: About to call os_log for webViewContainer success")
        print("✅ webViewContainer created and added to rootContainer")
        print("🔧 Step 3.1: webViewContainer success logged")

        if let tabBarView = self.tabBarView, tabBarView.superview == nil {
            rootContainer.addSubview(tabBarView)
            os_log("✅ tabBarView added to rootContainer", log: Self.log, type: .info)
        }

        // FIRST: Call onMiniappOpened to let Rust create the WebView
        os_log("🔧 Calling onMiniappOpened for appId: %@ path: %@", log: Self.log, type: .info, appId, initialPath)
        let openResult = onMiniappOpened(appId, initialPath)
        os_log("✅ onMiniappOpened completed with result: %d", log: Self.log, type: .info, openResult)

        // THEN: Try to find the WebView created by Rust
        os_log("🔧 Calling findWebView FFI: appId=%@, path=%@", log: Self.log, type: .info, appId, initialPath)
        let findResult = findWebView(appId, initialPath)
        os_log("🔧 findWebView result: %@", log: Self.log, type: .info, String(describing: findResult))

        if findResult != 0 {
            // WebView found from Rust layer
            let webView = Unmanaged<WKWebView>.fromOpaque(UnsafeRawPointer(bitPattern: findResult)!).takeUnretainedValue()
            webView.translatesAutoresizingMaskIntoConstraints = false
            webView.navigationDelegate = self
            webViewContainer.addSubview(webView)
            self.currentWebView = webView

            os_log("✅ Using WebView from Rust layer", log: Self.log, type: .info)

            // Call onPageShow to trigger page loading
            onPageShow(appId, initialPath)
            os_log("✅ onPageShow called", log: Self.log, type: .info)
        } else {
            os_log("❌ No WebView found from Rust layer", log: Self.log, type: .error)
        }

        // 照搬 macosx 实验实现的复杂布局约束逻辑
        // WebView 直接在 StatusBar 下方 (使用自定义标题栏)
        var webViewConstraints = [
            webViewContainer.topAnchor.constraint(equalTo: statusBarBackground.bottomAnchor),
            webViewContainer.leadingAnchor.constraint(equalTo: rootContainer.leadingAnchor),
            webViewContainer.trailingAnchor.constraint(equalTo: rootContainer.trailingAnchor)
        ]

        if let tabBarView = self.tabBarView {
            // 简化处理：只支持底部 TabBar
            let tabBarConstraints = [
                tabBarView.leadingAnchor.constraint(equalTo: rootContainer.leadingAnchor),
                tabBarView.trailingAnchor.constraint(equalTo: rootContainer.trailingAnchor),
                tabBarView.bottomAnchor.constraint(equalTo: rootContainer.bottomAnchor),
                tabBarView.heightAnchor.constraint(equalToConstant: Self.TAB_BAR_HEIGHT)
            ]

            NSLayoutConstraint.activate(tabBarConstraints)

            // WebView 在 TabBar 上方
            webViewConstraints.append(webViewContainer.bottomAnchor.constraint(equalTo: tabBarView.topAnchor))
        } else {
            webViewConstraints.append(webViewContainer.bottomAnchor.constraint(equalTo: rootContainer.bottomAnchor))
        }

        NSLayoutConstraint.activate(webViewConstraints)

        if let webView = currentWebView {
            let webViewInternalConstraints = [
                webView.topAnchor.constraint(equalTo: webViewContainer.topAnchor),
                webView.leadingAnchor.constraint(equalTo: webViewContainer.leadingAnchor),
                webView.trailingAnchor.constraint(equalTo: webViewContainer.trailingAnchor),
                webView.bottomAnchor.constraint(equalTo: webViewContainer.bottomAnchor)
            ]
            NSLayoutConstraint.activate(webViewInternalConstraints)
        }

        os_log("✅ WebView setup completed with complex layout", log: Self.log, type: .info)
    }

    private func setupNotificationObservers() {
        // TODO: Set up notification observers
        os_log("Setting up notification observers for appId: %@", log: Self.log, type: .info, appId)
    }

    // MARK: - WKNavigationDelegate
    public func webView(_ webView: WKWebView, didStartProvisionalNavigation navigation: WKNavigation!) {
        os_log("📱 WebView started loading", log: Self.log, type: .info)
    }

    public func webView(_ webView: WKWebView, didCommit navigation: WKNavigation!) {
        os_log("📱 WebView committed navigation", log: Self.log, type: .info)
    }

    public func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        os_log("✅ WebView finished loading successfully", log: Self.log, type: .info)
    }

    public func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        os_log("❌ WebView failed to load: %@", log: Self.log, type: .error, error.localizedDescription)
    }

    public func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        os_log("❌ WebView provisional navigation failed: %@", log: Self.log, type: .error, error.localizedDescription)
    }

    private func setupTitleBarIntegration() {
        // Set up custom title bar integration
        if let window = view.window as? macOSLxAppWindow,
           let statusBar = statusBarBackground {
            window.setTitleBarView(statusBar)
            os_log("Title bar integration set up", log: Self.log, type: .info)
        }
    }

    // MARK: - TabBar Icon Helper
    private func setButtonIcon(button: NSButton, iconPath: String) {
        var image: NSImage?

        if iconPath.hasPrefix("SF:") {
            // SF Symbols
            let symbolName = String(iconPath.dropFirst(3))
            if #available(macOS 11.0, *) {
                image = NSImage(systemSymbolName: symbolName, accessibilityDescription: nil)
                image?.isTemplate = true
            }
        } else if iconPath.hasPrefix("/") {
            // 绝对路径
            image = NSImage(contentsOfFile: iconPath)
        } else {
            // 相对路径，从 Resources 目录加载
            image = NSImage(named: iconPath)
            if image == nil {
                let resourcesPath = getResourcesPath()
                let fullPath = "\(resourcesPath)/homelxapp/\(iconPath)"
                image = NSImage(contentsOfFile: fullPath)
            }
        }

        if let image = image {
            let iconSize = NSSize(width: 22, height: 22)
            let resizedImage = resizeImage(image, to: iconSize)
            button.image = resizedImage
        }
    }

    private func getResourcesPath() -> String {
        let executablePath = Bundle.main.executablePath ?? ""
        let executableDir = (executablePath as NSString).deletingLastPathComponent
        return "\(executableDir)/Resources"
    }

    private func resizeImage(_ image: NSImage, to size: NSSize) -> NSImage {
        let resizedImage = NSImage(size: size)
        resizedImage.lockFocus()
        image.draw(in: NSRect(origin: .zero, size: size))
        resizedImage.unlockFocus()
        resizedImage.isTemplate = image.isTemplate
        return resizedImage
    }

    // MARK: - TabBar Actions
    @objc private func tabButtonClicked(_ sender: NSButton) {
        let index = sender.tag
        os_log("Tab button clicked: index=%d", log: Self.log, type: .info, index)

        // 这里应该根据 TabBar 配置获取对应的 pagePath
        // 暂时使用简化实现
        guard let tabBarConfigRust = getTabBarConfig(appId),
              let tabBarConfig = TabBarConfig.fromJson(tabBarConfigRust.toString()),
              index < tabBarConfig.items.count else {
            return
        }

        let item = tabBarConfig.items[index]
        switchPage(targetPath: item.pagePath)
    }

    // MARK: - Public methods for compatibility
    public func switchPage(targetPath: String) {
        self.initialPath = targetPath
        os_log("Switching to page: %@", log: lxAppViewControllerLog, type: .info, targetPath)

        // Call FFI to handle page switch
        let _ = onPageShow(appId, targetPath)
    }
}

#endif