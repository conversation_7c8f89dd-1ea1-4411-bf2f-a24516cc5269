#if os(macOS)
import Foundation
import Cocoa
import os.log
import CLingXiaFFI

/// macOS-specific LxApp implementation
@MainActor
public class macOSLxApp {
    private static let log = OSLog(subsystem: "LingXia", category: "macOSLxApp")
    
    private static var activeWindowControllers: [macOSLxAppWindowController] = []
    private static var hasInitialized = false
    
    /// Open home LxApp
    public static func openHomeLxApp() {
        guard let homeLxAppId = SharedLxApp.getHomeLxAppId() else {
            os_log("Home LxApp not configured", log: log, type: .error)
            return
        }

        let initialRoute = SharedLxApp.getHomeLxAppInitialRoute()
        openLxApp(appId: homeLxAppId, path: initialRoute)
    }
    
    /// Open specific LxApp
    public static func openLxApp(appId: String, path: String = "/") {
        os_log("Opening LxApp: %@ at path: %@", log: log, type: .info, appId, path)

        // Initialize LxApps if not already done
        macOSLxApp.initializeLxAppsIfNeeded()

        // Check if window already exists for this app
        if let existingController = activeWindowControllers.first(where: { $0.appId == appId }) {
            existingController.window?.makeKeyAndOrderFront(nil)
            switchPage(appId: appId, path: path)
            return
        }

        // Create window controller directly (like original implementation)
        let windowController = macOSLxAppWindowController(appId: appId, path: path)

        activeWindowControllers.append(windowController)

        os_log("Window controller created and should be visible", log: log, type: .info)
    }
    
    /// Close LxApp
    public static func closeLxApp(appId: String) {
        if let controller = activeWindowControllers.first(where: { $0.appId == appId }) {
            controller.window?.close()
        }
    }

    /// Switch to page in LxApp
    public static func switchPage(appId: String, path: String) {
        if let controller = activeWindowControllers.first(where: { $0.appId == appId }),
           let viewController = controller.window?.contentViewController as? macOSLxAppViewController {
            viewController.switchPage(targetPath: path)
        }
    }
    
    /// Remove window controller from active list
    internal static func removeWindowController(_ controller: macOSLxAppWindowController) {
        activeWindowControllers.removeAll { $0 === controller }
    }
    
    /// Get active window controllers
    internal static func getActiveWindowControllers() -> [macOSLxAppWindowController] {
        return activeWindowControllers
    }

    // MARK: - LxApps Initialization

    /// Initialize LxApps system if not already initialized
    internal static func initializeLxAppsIfNeeded() {
        // Check if already initialized
        if isInitialized {
            return
        }

        print("Initializing LxApps system...")

        // Get app-specific data and cache paths (not system Documents!)
        let appSupportPath = FileManager.default.urls(for: .applicationSupportDirectory, in: .userDomainMask).first?.path ?? ""
        let cachesPath = FileManager.default.urls(for: .cachesDirectory, in: .userDomainMask).first?.path ?? ""

        // Create app-specific subdirectory in Application Support
        let appDataPath = "\(appSupportPath)/LingXia"
        let appCachePath = "\(cachesPath)/LingXia"

        // Create directories if they don't exist
        try? FileManager.default.createDirectory(atPath: appDataPath, withIntermediateDirectories: true, attributes: nil)
        try? FileManager.default.createDirectory(atPath: appCachePath, withIntermediateDirectories: true, attributes: nil)

        print("App data path: \(appDataPath)")
        print("App cache path: \(appCachePath)")

        // Test if we can read app.json from bundle
        if let bundlePath = Bundle.main.path(forResource: "app", ofType: "json") {
            print("Found app.json at: \(bundlePath)")
            if let content = try? String(contentsOfFile: bundlePath) {
                print("app.json content: \(content)")
            } else {
                print("Failed to read app.json content")
            }
        } else {
            print("❌ app.json not found in bundle")
        }

        // Initialize the miniapp system
        let initResult = miniappInit(appDataPath, appCachePath)

        if let initResult = initResult {
            let initResultString = initResult.toString()
            print("✅ LxApps initialized successfully: \(initResultString)")

            // Parse the result like iOS does
            let parts = initResultString.components(separatedBy: ":")
            if parts.count >= 2 {
                let homeLxAppId = parts[0]
                let homeLxAppInitialRoute = Array(parts[1...]).joined(separator: ":")

                print("Home app ID: \(homeLxAppId)")
                print("Home app route: \(homeLxAppInitialRoute)")

                // Store in SharedLxApp for consistent access
                SharedLxApp.setHomeLxAppId(homeLxAppId)
                SharedLxApp.setHomeLxAppInitialRoute(homeLxAppInitialRoute)
            }

            _isInitialized = true
        } else {
            print("❌ Failed to initialize LxApps - miniappInit returned nil")
        }
    }

    /// Flag to track initialization state
    private static var _isInitialized = false

    /// Public getter for initialization state
    public static var isInitialized: Bool {
        return _isInitialized
    }

    /// Read homeLxAppID from app.json configuration
    private static func getHomeLxAppIDFromConfig() -> String? {
        guard let bundlePath = Bundle.main.path(forResource: "app", ofType: "json") else {
            print("❌ app.json not found in bundle")
            return nil
        }

        guard let content = try? String(contentsOfFile: bundlePath) else {
            print("❌ Failed to read app.json content")
            return nil
        }

        guard let data = content.data(using: .utf8),
              let json = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
              let homeLxAppID = json["homeLxAppID"] as? String else {
            print("❌ Failed to parse homeLxAppID from app.json")
            return nil
        }

        print("✅ Read homeLxAppID from app.json: \(homeLxAppID)")
        return homeLxAppID
    }
}



/// macOS-specific window
public class macOSLxAppWindow: NSWindow {
    internal var titleBarView: NSView?
    private var titleBarHeightConstraint: NSLayoutConstraint?
    private var contentViewTopConstraint: NSLayoutConstraint?

    public override init(contentRect: NSRect, styleMask style: NSWindow.StyleMask, backing backingStoreType: NSWindow.BackingStoreType, defer flag: Bool) {
        super.init(contentRect: contentRect, styleMask: [.titled, .closable, .miniaturizable], backing: backingStoreType, defer: flag)

        contentView?.wantsLayer = true
        contentView?.layer?.backgroundColor = NSColor.windowBackgroundColor.cgColor

        backgroundColor = NSColor.windowBackgroundColor
        isOpaque = true
        hasShadow = true

        // Key settings for custom appearance
        titlebarAppearsTransparent = true
        titleVisibility = .hidden
    }

    func setTitleBarView(_ view: NSView) {
        titleBarView?.removeFromSuperview()
        titleBarHeightConstraint?.isActive = false
        contentViewTopConstraint?.isActive = false

        titleBarView = view

        guard let contentView = contentView else {
            os_log("Failed to get content view", log: OSLog(subsystem: "LingXia", category: "LxAppWindow"), type: .error)
            return
        }

        view.wantsLayer = true
        view.layer?.zPosition = 999
        view.layer?.masksToBounds = false

        contentView.addSubview(view)
        view.translatesAutoresizingMaskIntoConstraints = false

        NSLayoutConstraint.activate([
            view.leadingAnchor.constraint(equalTo: contentView.leadingAnchor),
            view.trailingAnchor.constraint(equalTo: contentView.trailingAnchor),
            view.topAnchor.constraint(equalTo: contentView.topAnchor)
        ])

        let heightConstraint = view.heightAnchor.constraint(equalToConstant: 32) // Use 32 like working version
        heightConstraint.isActive = true
        titleBarHeightConstraint = heightConstraint

        layoutIfNeeded()
    }

    public override func layoutIfNeeded() {
        super.layoutIfNeeded()
        // Don't re-add titleBarView - it's already added in setTitleBarView
    }


}

#endif
