import Foundation
import CLingXiaFFI

#if os(iOS)
import UIKit

/// Main LxApp interface for iOS
@MainActor
public class LxApp {
    
    /// Initialize the LxApp system
    public static func initialize() {
        SharedLxApp.initialize()
    }
    
    /// Set home LxApp configuration
    public static func setHomeLxApp(appId: String, initialRoute: String = "/") {
        SharedLxApp.setHomeLxApp(appId: appId, initialRoute: initialRoute)
    }
    
    /// Set launch mode
    public static func setLaunchMode(_ mode: LxAppLaunchMode) {
        SharedLxApp.setLaunchMode(mode)
    }
    
    /// Open home LxApp
    public static func openHomeLxApp() {
        iOSLxApp.openHomeLxApp()
    }
    
    /// Open specific LxApp
    public static func openLxApp(appId: String, path: String = "/") {
        iOSLxApp.openLxApp(appId: appId, path: path)
    }
    
    /// Close LxApp
    public static func closeLxApp(appId: String) {
        iOSLxApp.closeLxApp(appId: appId)
    }
    
    /// Switch to page in LxApp
    public static func switchPage(appId: String, path: String) {
        iOSLxApp.switchPage(appId: appId, path: path)
    }
    
    /// Configure transparent system bars
    public static func configureTransparentSystemBars(viewController: UIViewController, lightStatusBarIcons: Bool = false) {
        iOSLxApp.configureTransparentSystemBars(viewController: viewController, lightStatusBarIcons: lightStatusBarIcons)
    }
}

#elseif os(macOS)
import Cocoa

/// Main LxApp interface for macOS
@MainActor
public class LxApp {
    
    /// Initialize the LxApp system
    public static func initialize() {
        SharedLxApp.initialize()
    }
    
    /// Set home LxApp configuration
    public static func setHomeLxApp(appId: String, initialRoute: String = "/") {
        SharedLxApp.setHomeLxApp(appId: appId, initialRoute: initialRoute)
    }
    
    /// Set window size for macOS
    public static func setWindowSize(width: CGFloat, height: CGFloat) {
        SharedLxApp.setWindowSize(width: width, height: height)
    }
    
    /// Open home LxApp
    public static func openHomeLxApp() {
        macOSLxApp.openHomeLxApp()
    }
    
    /// Open specific LxApp
    public static func openLxApp(appId: String, path: String = "/") {
        macOSLxApp.openLxApp(appId: appId, path: path)
    }
    
    /// Close LxApp
    public static func closeLxApp(appId: String) {
        macOSLxApp.closeLxApp(appId: appId)
    }
    
    /// Switch to page in LxApp
    public static func switchPage(appId: String, path: String) {
        macOSLxApp.switchPage(appId: appId, path: path)
    }
}

#endif
