import Foundation

#if os(iOS)
import UIKit
#elseif os(macOS)
import Cocoa
#endif

/// Tab bar item configuration
public struct TabBarItem {
    let text: String
    let iconPath: String?
    let selectedIconPath: String?
    let pagePath: String
    
    public init(text: String, iconPath: String?, selectedIconPath: String?, pagePath: String) {
        self.text = text
        self.iconPath = iconPath
        self.selectedIconPath = selectedIconPath
        self.pagePath = pagePath
    }
}

/// Tab bar configuration
public struct TabBarConfig {
    let hidden: Bool
    let color: PlatformColor?
    let selectedColor: PlatformColor?
    let backgroundColor: PlatformColor?
    let borderStyle: String?
    let items: [TabBarItem]
    
    static let DEFAULT_COLOR = PlatformColor.platformSecondaryLabel
    static let DEFAULT_SELECTED_COLOR = PlatformColor.platformLabel
    static let DEFAULT_BACKGROUND_COLOR = PlatformColor.platformBackground
    
    public init(
        hidden: Bool = false,
        color: PlatformColor? = nil,
        selectedColor: PlatformColor? = nil,
        backgroundColor: PlatformColor? = nil,
        borderStyle: String? = nil,
        items: [TabBarItem] = []
    ) {
        self.hidden = hidden
        self.color = color
        self.selectedColor = selectedColor
        self.backgroundColor = backgroundColor
        self.borderStyle = borderStyle
        self.items = items
    }
    
    public static func fromJson(_ json: String?) -> TabBarConfig? {
        guard let json = json, !json.isEmpty else {
            return TabBarConfig(hidden: true)
        }

        do {
            guard let data = json.data(using: .utf8),
                  let jsonObject = try JSONSerialization.jsonObject(with: data) as? [String: Any] else {
                return TabBarConfig(hidden: true)
            }

            let isHidden = jsonObject["hidden"] as? Bool ?? false
            let borderStyle = jsonObject["borderStyle"] as? String ?? "black"
            
            var items: [TabBarItem] = []
            if let itemsArray = jsonObject["list"] as? [[String: Any]] {
                items = itemsArray.compactMap { itemDict in
                    guard let text = itemDict["text"] as? String,
                          let pagePath = itemDict["pagePath"] as? String else {
                        return nil
                    }
                    
                    return TabBarItem(
                        text: text,
                        iconPath: itemDict["iconPath"] as? String,
                        selectedIconPath: itemDict["selectedIconPath"] as? String,
                        pagePath: pagePath
                    )
                }
            }

            return TabBarConfig(
                hidden: isHidden,
                color: parseColor(jsonObject["color"] as? String, defaultColor: DEFAULT_COLOR),
                selectedColor: parseColor(jsonObject["selectedColor"] as? String, defaultColor: DEFAULT_SELECTED_COLOR),
                backgroundColor: parseColor(jsonObject["backgroundColor"] as? String, defaultColor: DEFAULT_BACKGROUND_COLOR),
                borderStyle: borderStyle,
                items: items
            )
        } catch {
            return TabBarConfig(hidden: true)
        }
    }
    
    private static func parseColor(_ colorString: String?, defaultColor: PlatformColor) -> PlatformColor {
        guard let colorString = colorString, !colorString.isEmpty else {
            return defaultColor
        }
        return PlatformColor(hexString: colorString) ?? defaultColor
    }
}

/// Protocol for tab bar implementations
@MainActor
public protocol TabBarProtocol: AnyObject {
    var onTabSelectedListener: ((String) -> Void)? { get set }

    func updateConfig(_ config: TabBarConfig)
    func setSelectedTab(_ pagePath: String)
}

/// Shared tab bar constants
public struct TabBarConstants {
    public static let ITEM_FONT_SIZE: CGFloat = 10
    public static let ICON_SIZE: CGFloat = 22
    public static let ITEM_SPACING: CGFloat = 4
    public static let BORDER_WIDTH: CGFloat = 0.5
}
