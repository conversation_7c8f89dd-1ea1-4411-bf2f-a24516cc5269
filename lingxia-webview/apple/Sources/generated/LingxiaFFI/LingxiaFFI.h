// File automatically generated by swift-bridge.
#include <stdint.h>
#include <stdbool.h>
void* __swift_bridge__$miniapp_init(struct RustStr data_dir, struct RustStr cache_dir);
void __swift_bridge__$on_page_show(struct RustStr appid, struct RustStr path);
int32_t __swift_bridge__$on_miniapp_closed(struct RustStr appid);
void* __swift_bridge__$get_page_config(struct RustStr appid, struct RustStr path);
bool __swift_bridge__$on_back_pressed(struct RustStr appid);
int32_t __swift_bridge__$on_miniapp_opened(struct RustStr appid, struct RustStr path);
void* __swift_bridge__$get_tab_bar_config(struct RustStr appid);
uintptr_t __swift_bridge__$find_webview(struct RustStr appid, struct RustStr path);


