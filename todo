lingxia-ui: master/entry crate, liblingxia.so for android harmony etc
lingxia-webview: crate. export to manifest for apple
lingxia-miniapp: core crate
lingxia-user:  preserved for user and local
lingxia-mcp: server w/ sse

navibar: merge hidden into custome style.
lingxia builer:
  better name -> publish
  support bun etc. currently, hardcode 'npm'

about user agent
https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/web-default-useragent


native: 真的需要 load url 这个 API？ harmony 的 Web 入参是 src， webview。
native 只负责拦截等, create webview 在 UI 端完成。
?????????: harmony native 发起创建推送 tsfn， 延迟太大(one solution: precreate run in async, and wait creating done by tsfn callback)

file reader: pdf, excel, word, png, native API

devtools with MCP
1. capture log. realtime  with filter susbsytem/category
2. tools for view: click etc. opendia
3. capture, switch Page(tab)
4. hotreload -- upgrade miniapp packages and restart
5. how to capture request/response for logic fetch

integrate to cloud:
1. download and update
2. login(user, device info)
3. remote log trace(appid, path). collect and zip, upload to cloud
4. cloud function like wechat. lx.cloud.callFunc("name", {})

mobile api
finger print(unique device id)
    figure/device id:
      bind user. after done, next time, only input mobile phone to verify it's in org, device id same, auto login
      bind store with public IP & location
location(integrated)
camera, photo album, qr(harmony: arkts vs native)
deeplink(open lingxia app in dingtak etc, integrated)
push message(huawei, ios, xiaomi, oppo)
audio(lower priority)
huawei login (free, get mobile phone number ?)

platform:
support private crate, add allow attch JS API to JS worker. ！！！！！！！！
builder
App event: on_backgorupd, on_capture

LX JS API:
navigator, tabbar, location ...
体验版,开发版本 基础版本号
reopen miniapp: close(destroy?) and start
OnScrollChangeListener(lower)

Rong:
  Harmony JVM(code done, but not test)
  SSE
  ReadableStream
  V8

extra(lower priority):
printer support
scanner



tauri  icon:
https://github.com/tauri-apps/tauri/blob/cf0b3588a312d9c25ea49e82d40675ea94fcbfdd/crates/tauri-codegen/src/context.rs#L465
https://github.com/tauri-apps/tauri/blob/cf0b3588a312d9c25ea49e82d40675ea94fcbfdd/crates/tauri-utils/src/config.rs#L1225

lx:// Get
https: Get or others. -> is allowed domain ->check lx:// https://  yes, continue to go
                      -> is should proxed -> yes. build Request -> let miniapp to proxy

                          not allowed: reject directly
                          allow: proxyed, or direct to response

                          continue to process
                          take over then proces

                          ios: does not support intercept resoruce request in https ?
                          anroid: only supprot get method ?
